<template>
  <div class="maintenance-example">
    <h2>Maintenance Status Example</h2>
    
    <!-- Maintenance Status Display -->
    <div class="status-section">
      <h3>Current Maintenance Status</h3>
      <div v-if="loadings.getMaintenanceStatus" class="loading">
        Loading maintenance status...
      </div>
      <div v-else-if="errors.getMaintenanceStatus" class="error">
        Error: {{ errors.getMaintenanceStatus.message || 'Failed to load maintenance status' }}
      </div>
      <div v-else-if="maintenanceStatus" class="status-info">
        <div class="status-item">
          <strong>Under Maintenance:</strong> 
          <span :class="{ 'maintenance-active': isUnderMaintenance, 'maintenance-inactive': !isUnderMaintenance }">
            {{ isUnderMaintenance ? 'Yes' : 'No' }}
          </span>
        </div>
        <div class="status-item">
          <strong>Currently Active:</strong> 
          <span :class="{ 'maintenance-active': isMaintenanceActive, 'maintenance-inactive': !isMaintenanceActive }">
            {{ isMaintenanceActive ? 'Yes' : 'No' }}
          </span>
        </div>
        <div v-if="maintenanceMessage" class="status-item">
          <strong>Message:</strong> {{ maintenanceMessage }}
        </div>
        <div v-if="maintenanceStartDate" class="status-item">
          <strong>Start Date:</strong> {{ formatDate(maintenanceStartDate) }}
        </div>
        <div v-if="maintenanceEndDate" class="status-item">
          <strong>End Date:</strong> {{ formatDate(maintenanceEndDate) }}
        </div>
      </div>
      <div v-else class="no-data">
        No maintenance status data available
      </div>
    </div>

    <!-- Controls -->
    <div class="controls-section">
      <h3>Controls</h3>
      <div class="control-group">
        <label for="tenantId">Tenant ID:</label>
        <input 
          id="tenantId"
          v-model="customTenantId" 
          type="text" 
          placeholder="Enter tenant ID (optional)"
          class="tenant-input"
        />
      </div>
      <div class="button-group">
        <button 
          @click="checkMaintenanceStatus" 
          :disabled="loadings.getMaintenanceStatus"
          class="btn btn-primary"
        >
          Check Maintenance Status
        </button>
        <button 
          @click="quickCheck" 
          :disabled="loadings.getMaintenanceStatus"
          class="btn btn-secondary"
        >
          Quick Check (Boolean)
        </button>
        <button 
          @click="clearStatus" 
          class="btn btn-danger"
        >
          Clear Status
        </button>
      </div>
    </div>

    <!-- Quick Check Result -->
    <div v-if="quickCheckResult !== null" class="quick-check-section">
      <h3>Quick Check Result</h3>
      <div :class="{ 'maintenance-active': quickCheckResult, 'maintenance-inactive': !quickCheckResult }">
        Maintenance Active: {{ quickCheckResult ? 'Yes' : 'No' }}
      </div>
    </div>

    <!-- Raw Data -->
    <div v-if="maintenanceStatus" class="raw-data-section">
      <h3>Raw API Response</h3>
      <pre>{{ JSON.stringify(maintenanceStatus, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useMaintenanceStore } from '@/stores/maintenance'
import { useAuthStore } from '@/stores/auth'

const maintenanceStore = useMaintenanceStore()
const authStore = useAuthStore()

const {
  maintenanceStatus,
  loadings,
  errors,
  isUnderMaintenance,
  maintenanceMessage,
  maintenanceStartDate,
  maintenanceEndDate,
  isMaintenanceActive,
} = storeToRefs(maintenanceStore)

const customTenantId = ref('')
const quickCheckResult = ref<boolean | null>(null)

const checkMaintenanceStatus = async () => {
  quickCheckResult.value = null
  const tenantId = customTenantId.value || authStore.tenantId
  await maintenanceStore.getMaintenanceStatus(tenantId)
}

const quickCheck = async () => {
  const tenantId = customTenantId.value || authStore.tenantId
  quickCheckResult.value = await maintenanceStore.checkMaintenance(tenantId)
}

const clearStatus = () => {
  maintenanceStore.clearMaintenanceStatus()
  quickCheckResult.value = null
}

const formatDate = (dateString: string) => {
  try {
    return new Date(dateString).toLocaleString()
  } catch {
    return dateString
  }
}
</script>

<style scoped>
.maintenance-example {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.status-section,
.controls-section,
.quick-check-section,
.raw-data-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.status-item {
  margin-bottom: 10px;
}

.maintenance-active {
  color: #d32f2f;
  font-weight: bold;
}

.maintenance-inactive {
  color: #388e3c;
  font-weight: bold;
}

.loading {
  color: #1976d2;
  font-style: italic;
}

.error {
  color: #d32f2f;
  background: #ffebee;
  padding: 10px;
  border-radius: 4px;
}

.no-data {
  color: #666;
  font-style: italic;
}

.control-group {
  margin-bottom: 15px;
}

.control-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.tenant-input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #1565c0;
}

.btn-secondary {
  background: #757575;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #616161;
}

.btn-danger {
  background: #d32f2f;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c62828;
}

.quick-check-section {
  text-align: center;
  font-size: 18px;
}

.raw-data-section pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
