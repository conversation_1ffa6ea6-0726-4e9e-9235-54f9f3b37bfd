import { defineStore, storeToRefs } from 'pinia'
import { useSettingsStore } from '@/stores/settings'
import { useAuthStore } from '@/stores/auth'
import { useAPI } from '@/composables/useAPI'
import { nextTick } from 'vue'

export const useChatStore = defineStore('chatStore', {
  state: () => ({
    session_id: null as string | null,
    helpful_flag: null as boolean | null,
    last_turn: false as boolean | null,
    chats: [] as any[],
    loadings: {} as Record<string, boolean>,
    errors: {} as Record<string, any>,
    query: '' as string,
    messageListRef: null as any,
    messageBoxRef: null as any,
    showConfirmReset: false,
  }),
  getters: {},
  actions: {
    async chatInit() {
      const settingsStore = useSettingsStore()

      // Reset chat state
      this.session_id = null
      this.helpful_flag = null
      this.last_turn = false
      this.chats = []
      this.showConfirmReset = false

      try {
        await settingsStore.getBasicSettings()
        return true
      } catch (error: any) {
        console.error('Failed to load basic settings:', error)
        return false
      } finally {
        // Always add welcome message after attempting to load settings
        // This ensures welcome_message is added whether settings load succeeds or fails
        nextTick(() => {
          const { welcome_message } = storeToRefs(settingsStore)
          const welcomeMsg =
            welcome_message.value || 'こんにちは！何かお手伝いできることはありますか？'
          // if chats is empty, add welcome message
          if (this.chats.length === 0) {
            this.chats.push({
              chat_id: '0',
              message: welcomeMsg,
              type: 'ai',
              datetime: new Date(),
            })
          }
        })
      }
    },

    // Focus on message box input
    focusMessageBox() {
      nextTick(() => {
        if (this.messageBoxRef) {
          this.messageBoxRef.focus()
        }
      })
    },

    addMessage(message: any, options?: { chat_id?: string; type?: string }) {
      this.chats.push({
        chat_id: options?.chat_id || '0',
        message: message,
        type: options?.type || 'ai',
        datetime: new Date(),
      })
    },

    async chat(query: string) {
      const authStore = useAuthStore()
      try {
        this.query = ''
        const { envId, env } = authStore
        this.loadings.chat = true
        this.errors.chat = null
        this.chats.push({
          chat_id: '0',
          message: query,
          type: 'human',
          datetime: new Date(),
        })
        let apiUrl = '/v1/chatbot'
        if (env) {
          apiUrl = '/v1/chatbot/' + env
        } else if (envId) {
          apiUrl = '/v1/chatbot/env/' + envId
        }
        const response = await useAPI().ragService.post(apiUrl, {
          session_id: this.session_id,
          query,
        })
        const lastMessage = response.data?.chat_history?.slice(-1)[0]
        if (lastMessage) {
          this.chats.push({
            chat_id: lastMessage?.chat_id,
            message: lastMessage?.message,
            type: lastMessage?.type,
            datetime: lastMessage?.datetime || new Date(),
          })
        }
        this.session_id = response.data?.session_id
        this.helpful_flag = response.data?.helpful_flag
        this.last_turn = response.data?.last_turn

        if (this.helpful_flag) {
          this.createSuveyMessage(lastMessage?.chat_id)
        }
        return true
      } catch (error: any) {
        console.log('🚀 ~ chat ~ error:', error)
        this.errors.chat = error?.response?.data || error
        this.createErrorMessage(error?.response?.data?.error_code)
        return false
      } finally {
        this.loadings.chat = false
        // scoll to bottom
        this.scrollToBottom()
      }
    },

    scrollToBottom() {
      if (this.messageListRef) {
        nextTick(() => {
          this.messageListRef.scrollTo({
            top: this.messageListRef.scrollHeight,
            behavior: 'smooth',
          })
        })
      }
    },

    createSuveyMessage(chat_id: string) {
      const settingsStore = useSettingsStore()
      this.chats.push({
        chat_id,
        message: 'お役に立てましたか？',
        type: 'ai',
        datetime: new Date(),
        survey_options: settingsStore.survey_options,
      })
    },

    markSurveyDone(chat_id: string) {
      // mark survey done all chat messages having the same chat_id
      this.chats = this.chats.map((chat) => {
        if (chat.chat_id === chat_id) {
          chat.survey_done = true
        }
        return chat
      })
    },

    createErrorMessage(error_code: string) {
      const settingsStore = useSettingsStore()
      let errorMessage = settingsStore.defaultErrorMessage

      // Safely find error message if error_messages is an array
      if (Array.isArray(settingsStore.error_messages)) {
        const foundError = settingsStore.error_messages.find(
          (obj: any) => obj.error_code === error_code,
        )
        if (foundError?.message) {
          errorMessage = foundError.message
        }
      }

      this.chats.push({
        chat_id: '0',
        message: errorMessage,
        type: 'ai',
        datetime: new Date(),
        messageType: 'error',
        error_code,
      })
    },
  },
})
