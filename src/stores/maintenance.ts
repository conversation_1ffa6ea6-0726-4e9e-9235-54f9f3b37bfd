import { defineStore } from 'pinia'
import { useAPI } from '@/composables/useAPI'
import { useAuthStore } from '@/stores/auth'

export interface MaintenanceStatus {
  maintenance: boolean
  message: string
  start_date: string
  end_date: string
}

export const useMaintenanceStore = defineStore('maintenanceStore', {
  state: () => ({
    maintenanceStatus: null as MaintenanceStatus | null,
    loadings: {} as Record<string, boolean>,
    errors: {} as Record<string, any>,
  }),

  getters: {
    /**
     * Check if the system is currently under maintenance
     */
    isUnderMaintenance: (state): boolean => {
      const isMaintenanceActive = import.meta.env.VITE_MAINTENANCE_ACTIVE === 'true'
      return state.maintenanceStatus?.maintenance || isMaintenanceActive
    },

    /**
     * Get the maintenance message
     */
    maintenanceMessage: (state): string => {
      return (
        state.maintenanceStatus?.message ||
        'ただいまメンテナンス中です。\nしばらくしてもう一度お試しください。'
      )
    },

    /**
     * Get the maintenance start date
     */
    maintenanceStartDate: (state): string | null => {
      return state.maintenanceStatus?.start_date || null
    },

    /**
     * Get the maintenance end date
     */
    maintenanceEndDate: (state): string | null => {
      return state.maintenanceStatus?.end_date || null
    },

    /**
     * Check if maintenance is currently active based on dates
     */
    isMaintenanceActive: (state): boolean => {
      const isMaintenanceActiveFromConfig = import.meta.env.VITE_MAINTENANCE_ACTIVE === 'true'
      if (isMaintenanceActiveFromConfig) {
        return true
      }

      if (!state.maintenanceStatus?.maintenance) {
        return false
      }

      const now = new Date()
      const startDate = state.maintenanceStatus.start_date
        ? new Date(state.maintenanceStatus.start_date)
        : null
      const endDate = state.maintenanceStatus.end_date
        ? new Date(state.maintenanceStatus.end_date)
        : null

      // If no dates are provided, assume maintenance is active if maintenance flag is true
      if (!startDate && !endDate) {
        return state.maintenanceStatus.maintenance
      }

      // Check if current time is within maintenance window
      const isAfterStart = !startDate || now >= startDate
      const isBeforeEnd = !endDate || now <= endDate

      return state.maintenanceStatus.maintenance && isAfterStart && isBeforeEnd
    },
  },

  actions: {
    /**
     * Get maintenance status for a specific tenant ID
     */
    async getMaintenanceStatus(tenantId?: string): Promise<MaintenanceStatus | null> {
      const authStore = useAuthStore()

      // Use provided tenantId or get from auth store
      const targetTenantId = tenantId || authStore.tenantId

      if (!targetTenantId) {
        console.warn('No tenant ID provided for maintenance status check')
        return null
      }

      try {
        this.loadings.getMaintenanceStatus = true
        this.errors.getMaintenanceStatus = null

        const response = await useAPI().maintenanceService.get(`/tenants/${targetTenantId}`)

        this.maintenanceStatus = response.data as MaintenanceStatus

        return this.maintenanceStatus
      } catch (error: any) {
        console.error('Failed to get maintenance status:', error)
        this.errors.getMaintenanceStatus = error?.response?.data || error

        // Return null on error, but don't throw to allow graceful handling
        return null
      } finally {
        this.loadings.getMaintenanceStatus = false
      }
    },

    /**
     * Check maintenance status and return boolean for quick checks
     */
    async checkMaintenance(tenantId?: string): Promise<boolean> {
      await this.getMaintenanceStatus(tenantId)
      return this.isMaintenanceActive
    },

    /**
     * Clear maintenance status
     */
    clearMaintenanceStatus() {
      this.maintenanceStatus = null
      this.errors.getMaintenanceStatus = null
    },

    /**
     * Reset the entire store state
     */
    reset() {
      this.maintenanceStatus = null
      this.loadings = {}
      this.errors = {}
    },
  },
})
