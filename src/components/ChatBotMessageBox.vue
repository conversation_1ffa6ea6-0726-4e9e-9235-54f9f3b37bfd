<template>
  <form
    @submit.prevent="onChat"
    class="message-box-form"
    :class="{ 'fullscreen-message-box': isFullScreen }"
  >
    <label for="chat" class="aiko-sr-only"> メッセージを入力してください </label>
    <div
      class="aiko-flex aiko-items-center aiko-pr-2 message-box-container aiko-border-gray-100 aiko-border-t"
    >
      <textarea
        ref="localMessageBoxRef"
        v-model="query"
        id="chat"
        rows="1"
        class="aiko-max-h-20 aiko-min-h-10 aiko-block aiko-mx-2 aiko-p-2.5 aiko-w-full aiko-text-gray-900 aiko-bg-white aiko-rounded-lg aiko-border aiko-border-gray-300 message-input aiko-no-zoom"
        :class="[`aiko-focus:ring-${color_primary}-600`, `aiko-focus:border-${color_primary}-600`]"
        :placeholder="loadings['chat'] ? '回答中...' : `メッセージを入力してください`"
        @keydown="onKeyDown"
        :disabled="loadings['chat']"
        @focus="preventZoom"
      ></textarea>
      <button
        type="submit"
        class="aiko-send-button aiko-inline-flex aiko-justify-center aiko-p-2 aiko-rounded-full aiko-cursor-pointer"
        :class="[`aiko-text-${color_primary}-600`]"
        :disabled="loadings['chat']"
        :style="{
          zIndex: 99999999,
        }"
        @click="onChat"
      >
        <Icon
          :icon="loadings['chat'] ? 'eos-icons:loading' : 'material-symbols:send'"
          class="aiko-text-2xl"
        />
        <span class="aiko-sr-only">Send message</span>
      </button>
    </div>
  </form>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/settings'
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useChatStore } from '@/stores/chat'
import { nextTick } from 'vue'

const chatStore = useChatStore()
const settingsStore = useSettingsStore()
const { color_primary, isFullScreen } = storeToRefs(settingsStore)

const { query, loadings } = storeToRefs(chatStore)

// Local ref for the textarea element
const localMessageBoxRef = ref<HTMLTextAreaElement | null>(null)

// Set up the ref in the chat store when component is mounted
onMounted(() => {
  if (localMessageBoxRef.value) {
    chatStore.messageBoxRef = localMessageBoxRef.value
  }
})

const onChat = async () => {
  if (query.value.trim() === '') {
    return
  }
  await chatStore.chat(query.value)
  // Focus back on the message box after sending
  nextTick(() => {
    localMessageBoxRef.value?.focus()
  })
}

const onKeyDown = (event: KeyboardEvent) => {
  // submit on shift + enter
  if (event.key === 'Enter' && !event.shiftKey && !event.isComposing) {
    event.preventDefault()
    onChat()
  }
}

// Function to prevent iOS zoom on focus
const preventZoom = () => {
  // This is a no-op function, the actual prevention is done via CSS and meta viewport
  // But we need this function to satisfy the TypeScript check
}
</script>

<style>
.message-box-container {
  background-color: rgba(249, 250, 251, 0.7) !important;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
  position: relative;
  z-index: 10;
  padding-top: 5px;
}

/* Dark mode styles for message box container */
.dark .message-box-container {
  background-color: rgba(30, 30, 30, 0.7) !important;
  border-top: 1px solid rgba(50, 50, 50, 0.5);
}

.message-box-form {
  position: sticky;
  bottom: 0;
  width: 100%;
  z-index: 100;
}

.fullscreen-message-box {
  position: fixed;
  bottom: 20px;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 10001;
}

.message-input {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(209, 213, 219, 0.8);
  color: #333;
}

/* Dark mode styles for message input */
.dark .message-input {
  background-color: rgba(50, 50, 50, 0.9) !important;
  border: 1px solid rgba(70, 70, 70, 0.8);
  color: #f0f0f0 !important;
}
</style>

<style lang="css" scoped>
#chat {
  /** disable ring */
  outline: none;
}

/* Prevent iOS zoom on focus */
.no-zoom {
  font-size: 16px !important; /* Minimum font size to prevent iOS zoom */
}
</style>
