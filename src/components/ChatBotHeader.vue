<template>
  <div
    class="aiko-py-3 aiko-px-2 aiko-text-white aiko-flex aiko-flex-row aiko-rounded-t-xl chat-header-transparent aiko-items-center"
    :class="[`aiko-bg-${color_primary}-600`]"
  >
    <div class="aiko-w-20">
      <Icon
        v-if="!hasLiffId"
        icon="material-symbols:close-rounded"
        class="aiko-text-2xl aiko-cursor-pointer"
        @click.stop="onClose"
      />
    </div>
    <div
      class="aiko-w-full aiko-text-center aiko-cursor-grab aiko-select-none aiko-truncate"
      @mousedown.stop="$emit('header-drag', $event)"
      @touchstart.stop="$emit('header-drag', $event)"
    >
      {{ chatbot_name }}
    </div>
    <div class="aiko-w-20 aiko-flex aiko-flex-row aiko-justify-end aiko-gap-2">
      <Icon
        @click.stop="showConfirmReset = true"
        icon="bx:reset"
        class="aiko-text-2xl aiko-text-white aiko-p-0.5 aiko-cursor-pointer aiko-rounded-full"
        title="Reset chat"
      />
      <Icon
        v-if="shouldShowFullScreenButton"
        @click.stop="toggleFullScreen"
        :icon="isFullScreen ? 'material-symbols:fullscreen-exit' : 'material-symbols:fullscreen'"
        class="aiko-text-2xl aiko-text-white aiko-p-0.5 aiko-cursor-pointer aiko-rounded-full"
        :title="isFullScreen ? 'Exit full screen' : 'Full screen'"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/settings'
import { useChatStore } from '@/stores/chat'
import { useAuthStore } from '@/stores/auth'
// Removed unused imports
import { storeToRefs } from 'pinia'
import { computed, ref, onMounted, onUnmounted } from 'vue'

const settingsStore = useSettingsStore()
const authStore = useAuthStore()
const { color_primary, chatbot_name, isOpen, isFullScreen } = storeToRefs(settingsStore)
const { showConfirmReset } = storeToRefs(useChatStore())
const { liffId } = storeToRefs(authStore)

// Reactive variable to track if device is mobile or window is small
const isMobileOrSmallWindow = ref(false)

// Function to check if device is mobile or window is small
const checkMobileOrSmallWindow = () => {
  // Check if window width is less than 768px (typical tablet breakpoint)
  // or check if it's a mobile device using userAgent
  isMobileOrSmallWindow.value =
    window.innerWidth < 768 ||
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// Computed property to check if liffId has a value
const hasLiffId = computed(() => !!liffId.value)

// Computed property to determine if fullscreen button should be shown
const shouldShowFullScreenButton = computed(() => {
  // Hide button if hasLiffId is true (when liffId is present)
  if (hasLiffId.value) return false

  // Hide button if device is mobile or window is small
  if (isMobileOrSmallWindow.value) return false

  // Show button on large screens (both in fullscreen and normal mode)
  return true
})

// Function to toggle full-screen mode
const toggleFullScreen = () => {
  settingsStore.isFullScreen = !settingsStore.isFullScreen
}

const onClose = () => {
  isOpen.value = false
  isFullScreen.value = false
}

// Set up resize listener
onMounted(() => {
  checkMobileOrSmallWindow()
  window.addEventListener('resize', checkMobileOrSmallWindow)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobileOrSmallWindow)
})
</script>

<style>
.chat-header-transparent {
  background-color: rgba(var(--color-primary-rgb, 14, 165, 233), 0.8) !important;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode styles for chat header */
.dark .chat-header-transparent {
  border-bottom: 1px solid rgba(50, 50, 50, 0.5);
  /* The background color is already set by the color_primary variable */
}
</style>
