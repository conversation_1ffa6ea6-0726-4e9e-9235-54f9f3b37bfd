<template>
  <div class="maintenance-container">
    <!-- Close Button -->
    <div v-if="!hasLiffId" class="maintenance-close-button">
      <Icon
        icon="material-symbols:close-rounded"
        class="close-icon"
        @click="closeChatbot"
        title="チャットを閉じる"
      />
    </div>

    <div class="maintenance-content">
      <!-- Maintenance Icon -->
      <div class="maintenance-icon">
        <Icon
          icon="streamline-cyber-color:maintenance"
          class="icon"
          :style="{
            margin: '0 auto',
          }"
        />
      </div>

      <!-- Maintenance Title -->
      <h3 class="maintenance-title">システムメンテナンス中</h3>

      <!-- Maintenance Message -->
      <div class="maintenance-message">
        <p v-if="maintenanceMessage" class="message-text">
          {{ maintenanceMessage }}
        </p>
        <p v-else class="message-text">
          現在システムメンテナンスを実施しております。<br />
          ご不便をおかけして申し訳ございません。
        </p>
      </div>

      <!-- Maintenance Schedule -->
      <div v-if="showSchedule" class="maintenance-schedule">
        <div v-if="maintenanceStartDate" class="schedule-item">
          <Icon icon="mdi:clock-start" class="schedule-icon" />
          <span class="schedule-label">開始時刻:</span>
          <span class="schedule-time">{{ formatDate(maintenanceStartDate) }}</span>
        </div>
        <div v-if="maintenanceEndDate" class="schedule-item">
          <Icon icon="mdi:clock-end" class="schedule-icon" />
          <span class="schedule-label">終了予定:</span>
          <span class="schedule-time">{{ formatDate(maintenanceEndDate) }}</span>
        </div>
      </div>

      <!-- Countdown Timer -->
      <div v-if="maintenanceEndDate && countdown" class="countdown-container">
        <div class="countdown-title">メンテナンス終了まで</div>
        <div class="countdown-timer">
          <div v-if="countdown.days > 0" class="countdown-item">
            <span class="countdown-number">{{ countdown.days }}</span>
            <span class="countdown-label">日</span>
          </div>
          <div v-if="countdown.hours > 0 || countdown.days > 0" class="countdown-item">
            <span class="countdown-number">{{ countdown.hours.toString().padStart(2, '0') }}</span>
            <span class="countdown-label">時間</span>
          </div>
          <div class="countdown-item">
            <span class="countdown-number">{{
              countdown.minutes.toString().padStart(2, '0')
            }}</span>
            <span class="countdown-label">分</span>
          </div>
          <div class="countdown-item">
            <span class="countdown-number">{{
              countdown.seconds.toString().padStart(2, '0')
            }}</span>
            <span class="countdown-label">秒</span>
          </div>
        </div>
        <div v-if="countdown.isExpired" class="countdown-expired">
          メンテナンス終了予定時刻を過ぎました
        </div>
      </div>

      <!-- Retry Button -->
      <div class="maintenance-actions">
        <button
          @click="checkStatus"
          :disabled="loadings.getMaintenanceStatus"
          class="retry-button"
          :class="{ loading: loadings.getMaintenanceStatus }"
        >
          <Icon
            :icon="loadings.getMaintenanceStatus ? 'eos-icons:loading' : 'mdi:refresh'"
            class="button-icon"
          />
          {{ loadings.getMaintenanceStatus ? '確認中...' : '状態を確認' }}
        </button>
      </div>

      <!-- Last Check Time -->
      <div v-if="lastCheckTime" class="last-check">最終確認: {{ formatTime(lastCheckTime) }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useMaintenanceStore } from '@/stores/maintenance'
import { useSettingsStore } from '@/stores/settings'
import { useAuthStore } from '@/stores/auth'
import { Icon } from '@iconify/vue'

const props = defineProps({
  showSchedule: {
    type: Boolean,
    default: true,
  },
  autoRefresh: {
    type: Boolean,
    default: true,
  },
  refreshInterval: {
    type: Number,
    default: 30000, // 30 seconds
  },
})

const maintenanceStore = useMaintenanceStore()
const settingsStore = useSettingsStore()
const authStore = useAuthStore()
const { maintenanceMessage, maintenanceStartDate, maintenanceEndDate, loadings } =
  storeToRefs(maintenanceStore)
const { liffId } = storeToRefs(authStore)

// Computed property to check if liffId has a value
const hasLiffId = computed(() => !!liffId.value)

const lastCheckTime = ref<Date | null>(null)
let refreshTimer: number | null = null
let countdownTimer: number | null = null
let expiredCheckTimer: number | null = null

// Countdown state
const countdown = ref<{
  days: number
  hours: number
  minutes: number
  seconds: number
  isExpired: boolean
} | null>(null)

// Close chatbot function
const closeChatbot = () => {
  settingsStore.isOpen = false
  settingsStore.isFullScreen = false
}

// Calculate countdown
const calculateCountdown = () => {
  if (!maintenanceEndDate.value) {
    countdown.value = null
    return
  }

  try {
    const endDate = new Date(maintenanceEndDate.value)
    const now = new Date()
    const timeDiff = endDate.getTime() - now.getTime()

    if (timeDiff <= 0) {
      // Countdown has expired
      const wasNotExpired = countdown.value && !countdown.value.isExpired

      countdown.value = {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        isExpired: true,
      }

      // Auto-check maintenance status when countdown just expired
      if (wasNotExpired) {
        console.log('Countdown expired, checking maintenance status...')
        setTimeout(() => {
          checkStatus()
        }, 2000) // Wait 2 seconds before checking to allow for server-side updates

        // Start periodic checking every 30 seconds when expired
        startExpiredCheck()
      }

      return
    }

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)

    countdown.value = {
      days,
      hours,
      minutes,
      seconds,
      isExpired: false,
    }
  } catch (error) {
    console.warn('Failed to calculate countdown:', error)
    countdown.value = null
  }
}

// Start countdown timer
const startCountdown = () => {
  if (maintenanceEndDate.value && !countdownTimer) {
    // Stop expired check when starting new countdown
    stopExpiredCheck()
    calculateCountdown()
    countdownTimer = setInterval(calculateCountdown, 1000)
  }
}

// Stop countdown timer
const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// Start expired check timer (check every 30 seconds when countdown expired)
const startExpiredCheck = () => {
  if (!expiredCheckTimer) {
    expiredCheckTimer = setInterval(() => {
      console.log('Checking maintenance status (countdown expired)...')
      checkStatus()
    }, 30000) // Check every 30 seconds
  }
}

// Stop expired check timer
const stopExpiredCheck = () => {
  if (expiredCheckTimer) {
    clearInterval(expiredCheckTimer)
    expiredCheckTimer = null
  }
}

const checkStatus = async () => {
  await maintenanceStore.getMaintenanceStatus()
  lastCheckTime.value = new Date()

  // Stop expired check if maintenance is no longer active
  if (!maintenanceStore.isMaintenanceActive) {
    stopExpiredCheck()
    console.log('Maintenance no longer active, stopped expired checking')
  }

  // Restart countdown if end date changed
  stopCountdown()
  startCountdown()
}

const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString)
    return date.toLocaleString('ja-JP', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Tokyo',
    })
  } catch {
    return dateString
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('ja-JP', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: 'Asia/Tokyo',
  })
}

const startAutoRefresh = () => {
  if (props.autoRefresh && !refreshTimer) {
    refreshTimer = setInterval(() => {
      checkStatus()
    }, props.refreshInterval)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

onMounted(() => {
  // Initial check
  checkStatus()

  // Start countdown timer
  startCountdown()

  // Start auto refresh if enabled
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
  stopCountdown()
  stopExpiredCheck()
})
</script>

<style scoped>
.maintenance-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #deed09 100%);
  padding: 20px;
  box-sizing: border-box;
  position: relative;
}

.maintenance-close-button {
  position: absolute;
  top: 20px;
  left: 25px;
  z-index: 1000;
}

.close-icon {
  font-size: 32px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.close-icon:hover {
  color: #333;
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.maintenance-content {
  background: white;
  border-radius: 16px;
  padding: 40px 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
  border: 1px solid #e1e5e9;
}

.maintenance-icon {
  margin-bottom: 20px;
}

.maintenance-icon .icon {
  font-size: 64px;
  color: #ff9800;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.maintenance-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.countdown-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding-top: 10px;
  padding-bottom: 15px;
  margin-bottom: 25px;
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.countdown-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
  opacity: 0.9;
}

.countdown-timer {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.countdown-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}

.countdown-number {
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.countdown-label {
  font-size: 12px;
  margin-top: 4px;
  opacity: 0.8;
  font-weight: 500;
}

.countdown-expired {
  margin-top: 15px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.maintenance-message {
  margin-bottom: 10px;
}

.message-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 0;
  white-space: pre-line;
}

.maintenance-schedule {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 10px;
  border: 1px solid #e9ecef;
}

.schedule-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-size: 14px;
}

.schedule-item:last-child {
  margin-bottom: 0;
}

.schedule-icon {
  font-size: 16px;
  color: #6c757d;
  margin-right: 8px;
}

.schedule-label {
  font-weight: 600;
  color: #495057;
  margin-right: 8px;
}

.schedule-time {
  color: #007bff;
  font-weight: 500;
}

.maintenance-actions {
  margin-bottom: 10px;
}

.retry-button {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.retry-button:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.retry-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.retry-button.loading .button-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.button-icon {
  font-size: 16px;
}

.last-check {
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}

/* Dark mode support */
.dark .maintenance-container {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.dark .maintenance-content {
  background: #1a202c;
  border-color: #2d3748;
  color: #e2e8f0;
}

.dark .maintenance-title {
  color: #f7fafc;
}

.dark .message-text {
  color: #cbd5e0;
}

.dark .close-icon {
  color: #e2e8f0;
  background: rgba(45, 55, 72, 0.9);
}

.dark .close-icon:hover {
  color: #f7fafc;
  background: rgba(45, 55, 72, 1);
}

.dark .countdown-container {
  background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
}

.dark .maintenance-schedule {
  background: #2d3748;
  border-color: #4a5568;
}

.dark .schedule-label {
  color: #e2e8f0;
}

.dark .last-check {
  color: #a0aec0;
}
</style>
