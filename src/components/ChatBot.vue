<template>
  <div
    class="aiko-relative aiko-h-full aiko-w-full aiko-flex aiko-flex-col aiko-rounded-xl aiko-overflow-hidden"
    :class="{ 'fullscreen-layout': isFullScreen }"
  >
    <ChatBotConfirm
      v-if="showConfirmReset"
      @on-cancel="showConfirmReset = false"
      @on-confirm="handleConfirmReset"
    />
    <ChatBotMaintenance v-else-if="isMaintenanceActive" :show-schedule="!!maintenanceEndDate" />
    <ChatBotLogin v-else-if="isNeedLogin" />
    <ChatBotError v-else-if="hasLoginError" :title="'ログイン失敗'" :message="loginErrorMessage" />
    <ChatBotError v-else-if="errorMessage" :title="'エラー'" :message="errorMessage" />
    <template v-else>
      <!-- <div class="p-2 text-center text-sm text-gray-600 font-medium">
        {{ chatbot_name }}
      </div> -->

      <ChatBotHeader @header-drag="$emit('header-drag', $event)" />
      <ChatBotMessageList :chats="chats" />

      <ChatBotRechatButton class="aiko-sticky aiko-bottom-0 aiko-w-full" v-if="last_turn" />
      <div class="aiko-footer-container">
        <ChatBotMessageBox class="aiko-sticky aiko-w-full" v-if="!last_turn" />
        <ChatBotFooter class="aiko-chatbot-footer" />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { useChatStore } from '@/stores/chat'
import { useAuthStore } from '@/stores/auth'
import { useSettingsStore } from '@/stores/settings'
import { useMaintenanceStore } from '@/stores/maintenance'
import { onMounted, ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import ChatBotMessageBox from '@/components/ChatBotMessageBox.vue'
import ChatBotRechatButton from '@/components/ChatBotRechatButton.vue'
import ChatBotMessageList from '@/components/ChatBotMessageList.vue'
import ChatBotLogin from '@/components/ChatBotLogin.vue'
import ChatBotConfirm from '@/components/ChatBotConfirm.vue'
import ChatBotHeader from '@/components/ChatBotHeader.vue'
import ChatBotError from '@/components/ChatBotError.vue'
import ChatBotFooter from '@/components/ChatBotFooter.vue'
import ChatBotMaintenance from '@/components/ChatBotMaintenance.vue'

// Define emits
const emit = defineEmits(['header-drag'])

const authStore = useAuthStore()
const { isLoggedIn, isNeedLogin } = storeToRefs(authStore)
const settingsStore = useSettingsStore()
const { isFullScreen, errorMessage } = storeToRefs(settingsStore)
const chatStore = useChatStore()
const { chats, last_turn, showConfirmReset } = storeToRefs(chatStore)
const maintenanceStore = useMaintenanceStore()
const { isMaintenanceActive, maintenanceEndDate } = storeToRefs(maintenanceStore)

// Error handling
const hasLoginError = ref(false)
const loginErrorMessage = ref(
  'Unable to connect to the chat service. Please check your connection and try again.',
)

// Handle confirm reset
const handleConfirmReset = async () => {
  await chatStore.chatInit()
}

onMounted(async () => {
  // Reset error state
  hasLoginError.value = false

  // Initialize chatbot
  authStore.initChatbot()
  // await settingsStore.getBasicSettings()

  // Try guest login if needed
  if (!isLoggedIn.value && !isNeedLogin.value) {
    const loginResult = await authStore.guestLogin()

    // Check for login errors
    if (!loginResult) {
      hasLoginError.value = true
      const errorData = authStore.errors.guestLogin

      // Format error message
      if (errorData) {
        if (typeof errorData === 'string') {
          loginErrorMessage.value = errorData
        } else if (errorData.message) {
          loginErrorMessage.value = errorData.message
        } else if (errorData.error) {
          loginErrorMessage.value = errorData.error
        } else {
          loginErrorMessage.value = 'Failed to connect to chat service. Please try again.'
        }
      }

      return // Stop initialization if login failed
    }
  }

  // Initialize chat if login was successful
  await chatStore.chatInit()
})

watch(
  () => isMaintenanceActive.value,
  (newValue) => {
    // console log
    console.log('chatbot maintenance status changed:', newValue)
  },
)
</script>

<style scoped lang="scss">
#llm-rag-chatbot .fullscreen-layout {
  display: flex !important;
  flex-direction: column !important;
  height: 100vh !important;

  .aiko-chatbot-footer {
    margin-bottom: 20px;
  }

  .aiko-sticky {
    bottom: 40px;
    width: 100% !important;
  }
}

#llm-rag-chatbot .fullscreen-layout .message-list-transparent {
  flex: 1 !important;
  overflow-y: auto !important;
}

.aiko-footer-container {
  background: white;
}

// Dark mode styles
.dark .aiko-footer-container {
  background: #141414;
}
</style>
