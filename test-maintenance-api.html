<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test Maintenance API Integration</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f0f0f0;
      }
      .test-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .test-section {
        margin-bottom: 30px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      .test-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      .test-button:hover {
        background: #0056b3;
      }
      .test-button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .status {
        margin-top: 10px;
        padding: 10px;
        border-radius: 5px;
        font-weight: bold;
      }
      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .status.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      .result-box {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        margin-top: 10px;
        font-family: monospace;
        white-space: pre-wrap;
        max-height: 200px;
        overflow-y: auto;
      }
      .api-list {
        background: #e7f3ff;
        border-left: 4px solid #007bff;
        padding: 15px;
        margin: 15px 0;
      }
      .api-list ul {
        margin: 0;
        padding-left: 20px;
      }
      .api-list li {
        margin-bottom: 8px;
      }
    </style>
    <script type="module" src="./dist/chatbot.min.js?tenantId=miyawaka"></script>
  </head>
  <body>
    <div class="test-container">
      <h1>🔧 Test Maintenance API Integration</h1>

      <div class="test-section">
        <h3>New Maintenance APIs</h3>
        <div class="api-list">
          <h4>📋 Available Methods:</h4>
          <ul>
            <li><strong>checkMaintenance(tenantId?)</strong> - Quick boolean check</li>
            <li><strong>getMaintenanceStatus(tenantId?)</strong> - Detailed status object</li>
            <li><strong>isMaintenanceActive()</strong> - Current active state</li>
            <li><strong>getMaintenanceMessage()</strong> - Current message</li>
            <li><strong>clearMaintenanceStatus()</strong> - Clear cached status</li>
          </ul>
          <p><strong>✨ Features:</strong></p>
          <ul>
            <li>Automatic initialization check</li>
            <li>Reset on reload/updateAuth</li>
            <li>Exposed in getInstance()</li>
          </ul>
        </div>
      </div>

      <div class="test-section">
        <h3>Test Controls</h3>

        <button class="test-button" onclick="initializeChatbot()" id="btnInit">
          Initialize Chatbot
        </button>
        <button class="test-button" onclick="testCheckMaintenance()" id="btnCheck">
          Check Maintenance
        </button>
        <button class="test-button" onclick="testGetStatus()" id="btnStatus">Get Status</button>
        <button class="test-button" onclick="testIsActive()" id="btnActive">Is Active</button>
        <button class="test-button" onclick="testGetMessage()" id="btnMessage">Get Message</button>
        <button class="test-button" onclick="testClearStatus()" id="btnClear">Clear Status</button>
        <button class="test-button" onclick="testGetInstance()" id="btnInstance">
          Get Instance
        </button>
      </div>

      <div class="test-section">
        <h3>Current Status</h3>
        <div id="currentStatus" class="status info">
          <div>Chatbot: <span id="chatbotStatus">Not Initialized</span></div>
          <div>Maintenance Store: <span id="storeStatus">Not Available</span></div>
          <div>Last Check: <span id="lastCheck">Never</span></div>
        </div>
      </div>

      <div class="test-section">
        <h3>API Results</h3>
        <div id="apiResults" class="result-box">Ready to test maintenance APIs...</div>
      </div>
    </div>

    <script type="module">
      let chatbot = null

      function log(message, type = 'info') {
        const resultsContainer = document.getElementById('apiResults')
        const timestamp = new Date().toLocaleTimeString()
        const typePrefix = type.toUpperCase().padEnd(7)
        resultsContainer.textContent += `\n[${timestamp}] ${typePrefix} ${message}`
        resultsContainer.scrollTop = resultsContainer.scrollHeight
      }

      function updateStatus() {
        document.getElementById('chatbotStatus').textContent = chatbot
          ? 'Initialized'
          : 'Not Initialized'

        if (chatbot) {
          const instance = chatbot.getInstance()
          document.getElementById('storeStatus').textContent = instance.maintenanceStore
            ? 'Available'
            : 'Not Available'
        }
      }

      window.initializeChatbot = async function () {
        try {
          log('Initializing chatbot with maintenance store...', 'system')

          chatbot = window.LLMRagChatbot.instance

          log('Chatbot initialized successfully', 'success')
          log('Maintenance store automatically initialized', 'info')
          log('Initial maintenance check completed', 'info')

          updateStatus()
          document.getElementById('lastCheck').textContent = 'On Initialize'
        } catch (error) {
          log(`Initialization failed: ${error.message}`, 'error')
          console.error('Initialization error:', error)
        }
      }

      window.testCheckMaintenance = async function () {
        if (!chatbot) {
          log('Chatbot not initialized!', 'error')
          return
        }

        try {
          log('Testing checkMaintenance()...', 'system')
          const result = await chatbot.checkMaintenance()
          log(`checkMaintenance() result: ${result}`, 'success')
          document.getElementById('lastCheck').textContent = 'checkMaintenance()'
        } catch (error) {
          log(`checkMaintenance() failed: ${error.message}`, 'error')
        }
      }

      window.testGetStatus = async function () {
        if (!chatbot) {
          log('Chatbot not initialized!', 'error')
          return
        }

        try {
          log('Testing getMaintenanceStatus()...', 'system')
          const result = await chatbot.getMaintenanceStatus()
          log(`getMaintenanceStatus() result:`, 'success')
          log(JSON.stringify(result, null, 2), 'info')
          document.getElementById('lastCheck').textContent = 'getMaintenanceStatus()'
        } catch (error) {
          log(`getMaintenanceStatus() failed: ${error.message}`, 'error')
        }
      }

      window.testIsActive = function () {
        if (!chatbot) {
          log('Chatbot not initialized!', 'error')
          return
        }

        try {
          log('Testing isMaintenanceActive()...', 'system')
          const result = chatbot.isMaintenanceActive()
          log(`isMaintenanceActive() result: ${result}`, 'success')
        } catch (error) {
          log(`isMaintenanceActive() failed: ${error.message}`, 'error')
        }
      }

      window.testGetMessage = function () {
        if (!chatbot) {
          log('Chatbot not initialized!', 'error')
          return
        }

        try {
          log('Testing getMaintenanceMessage()...', 'system')
          const result = chatbot.getMaintenanceMessage()
          log(`getMaintenanceMessage() result: "${result}"`, 'success')
        } catch (error) {
          log(`getMaintenanceMessage() failed: ${error.message}`, 'error')
        }
      }

      window.testClearStatus = function () {
        if (!chatbot) {
          log('Chatbot not initialized!', 'error')
          return
        }

        try {
          log('Testing clearMaintenanceStatus()...', 'system')
          chatbot.clearMaintenanceStatus()
          log('clearMaintenanceStatus() completed', 'success')
        } catch (error) {
          log(`clearMaintenanceStatus() failed: ${error.message}`, 'error')
        }
      }

      window.testGetInstance = function () {
        if (!chatbot) {
          log('Chatbot not initialized!', 'error')
          return
        }

        try {
          log('Testing getInstance()...', 'system')
          const instance = chatbot.getInstance()
          log('getInstance() result:', 'success')
          log(`- authStore: ${!!instance.authStore}`, 'info')
          log(`- chatStore: ${!!instance.chatStore}`, 'info')
          log(`- settingsStore: ${!!instance.settingsStore}`, 'info')
          log(`- maintenanceStore: ${!!instance.maintenanceStore}`, 'info')
          log(`- app: ${!!instance.app}`, 'info')
        } catch (error) {
          log(`getInstance() failed: ${error.message}`, 'error')
        }
      }

      // Auto-update status every 3 seconds
      setInterval(updateStatus, 3000)

      log('Maintenance API test page loaded.', 'system')
      log('Click "Initialize Chatbot" to begin testing.', 'info')
    </script>
  </body>
</html>
